-- NFL Stats Database Schema for Supabase
-- Run this in your Supabase SQL Editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create Quarterbacks table
CREATE TABLE IF NOT EXISTS quarterbacks (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    yards INTEGER DEFAULT 0,
    touchdowns INTEGER DEFAULT 0,
    interceptions INTEGER DEFAULT 0,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    completions INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    sacked INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Running Backs table
CREATE TABLE IF NOT EXISTS running_backs (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    attempts INTEGER DEFAULT 0,
    yards INTEGER DEFAULT 0,
    touchdowns INTEGER DEFAULT 0,
    yards_per_attempt DECIMAL(5,2) DEFAULT 0.00,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Wide Receivers table
CREATE TABLE IF NOT EXISTS wide_receivers (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    yards INTEGER DEFAULT 0,
    touchdowns INTEGER DEFAULT 0,
    receptions INTEGER DEFAULT 0,
    targets INTEGER DEFAULT 0,
    reception_percentage DECIMAL(5,2) DEFAULT 0.00,
    int_allowed INTEGER DEFAULT 0,
    snaps INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Tight Ends table
CREATE TABLE IF NOT EXISTS tight_ends (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    yards INTEGER DEFAULT 0,
    yac INTEGER DEFAULT 0,
    touchdowns INTEGER DEFAULT 0,
    reception_percentage DECIMAL(5,2) DEFAULT 0.00,
    catches INTEGER DEFAULT 0,
    targets INTEGER DEFAULT 0,
    sack_allowed INTEGER DEFAULT 0,
    snaps INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Centers table
CREATE TABLE IF NOT EXISTS centers (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    snaps INTEGER DEFAULT 0,
    sacks_allowed INTEGER DEFAULT 0,
    pressures_allowed INTEGER DEFAULT 0,
    qb_hits_allowed INTEGER DEFAULT 0,
    sps DECIMAL(5,2) DEFAULT 0.00,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Defensive Ends table
CREATE TABLE IF NOT EXISTS defensive_ends (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    sacks INTEGER DEFAULT 0,
    pressures INTEGER DEFAULT 0,
    qb_hits INTEGER DEFAULT 0,
    tackles INTEGER DEFAULT 0,
    safeties INTEGER DEFAULT 0,
    snaps INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Linebackers table
CREATE TABLE IF NOT EXISTS linebackers (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    interceptions INTEGER DEFAULT 0,
    tackles INTEGER DEFAULT 0,
    targets INTEGER DEFAULT 0,
    cmp_allowed INTEGER DEFAULT 0,
    cmp_percentage DECIMAL(5,2) DEFAULT 0.00,
    yda INTEGER DEFAULT 0,
    tda INTEGER DEFAULT 0,
    snaps INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Defensive Backs table
CREATE TABLE IF NOT EXISTS defensive_backs (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    interceptions INTEGER DEFAULT 0,
    tackles INTEGER DEFAULT 0,
    cmp_percentage DECIMAL(5,2) DEFAULT 0.00,
    cmp_allowed INTEGER DEFAULT 0,
    targets INTEGER DEFAULT 0,
    yda INTEGER DEFAULT 0,
    tda INTEGER DEFAULT 0,
    snaps INTEGER DEFAULT 0,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Kickers table
CREATE TABLE IF NOT EXISTS kickers (
    id SERIAL PRIMARY KEY,
    team VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    completions INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    longest INTEGER DEFAULT 0,
    fg_percentage DECIMAL(5,2) DEFAULT 0.00,
    team_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quarterbacks_team ON quarterbacks(team);
CREATE INDEX IF NOT EXISTS idx_quarterbacks_yards ON quarterbacks(yards DESC);
CREATE INDEX IF NOT EXISTS idx_quarterbacks_touchdowns ON quarterbacks(touchdowns DESC);

CREATE INDEX IF NOT EXISTS idx_running_backs_team ON running_backs(team);
CREATE INDEX IF NOT EXISTS idx_running_backs_yards ON running_backs(yards DESC);
CREATE INDEX IF NOT EXISTS idx_running_backs_touchdowns ON running_backs(touchdowns DESC);

CREATE INDEX IF NOT EXISTS idx_wide_receivers_team ON wide_receivers(team);
CREATE INDEX IF NOT EXISTS idx_wide_receivers_yards ON wide_receivers(yards DESC);
CREATE INDEX IF NOT EXISTS idx_wide_receivers_touchdowns ON wide_receivers(touchdowns DESC);

CREATE INDEX IF NOT EXISTS idx_tight_ends_team ON tight_ends(team);
CREATE INDEX IF NOT EXISTS idx_tight_ends_yards ON tight_ends(yards DESC);
CREATE INDEX IF NOT EXISTS idx_tight_ends_touchdowns ON tight_ends(touchdowns DESC);

CREATE INDEX IF NOT EXISTS idx_centers_team ON centers(team);
CREATE INDEX IF NOT EXISTS idx_centers_sps ON centers(sps DESC);

CREATE INDEX IF NOT EXISTS idx_defensive_ends_team ON defensive_ends(team);
CREATE INDEX IF NOT EXISTS idx_defensive_ends_sacks ON defensive_ends(sacks DESC);
CREATE INDEX IF NOT EXISTS idx_defensive_ends_tackles ON defensive_ends(tackles DESC);

CREATE INDEX IF NOT EXISTS idx_linebackers_team ON linebackers(team);
CREATE INDEX IF NOT EXISTS idx_linebackers_interceptions ON linebackers(interceptions DESC);
CREATE INDEX IF NOT EXISTS idx_linebackers_tackles ON linebackers(tackles DESC);

CREATE INDEX IF NOT EXISTS idx_defensive_backs_team ON defensive_backs(team);
CREATE INDEX IF NOT EXISTS idx_defensive_backs_interceptions ON defensive_backs(interceptions DESC);
CREATE INDEX IF NOT EXISTS idx_defensive_backs_tackles ON defensive_backs(tackles DESC);

CREATE INDEX IF NOT EXISTS idx_kickers_team ON kickers(team);
CREATE INDEX IF NOT EXISTS idx_kickers_fg_percentage ON kickers(fg_percentage DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to update updated_at automatically
CREATE TRIGGER update_quarterbacks_updated_at BEFORE UPDATE ON quarterbacks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_running_backs_updated_at BEFORE UPDATE ON running_backs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wide_receivers_updated_at BEFORE UPDATE ON wide_receivers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tight_ends_updated_at BEFORE UPDATE ON tight_ends FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_centers_updated_at BEFORE UPDATE ON centers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_defensive_ends_updated_at BEFORE UPDATE ON defensive_ends FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_linebackers_updated_at BEFORE UPDATE ON linebackers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_defensive_backs_updated_at BEFORE UPDATE ON defensive_backs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kickers_updated_at BEFORE UPDATE ON kickers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create ranking views for each position
-- QB Rankings (by yards, then TDs, then completion %)
CREATE OR REPLACE VIEW qb_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY yards DESC, touchdowns DESC, completion_percentage DESC) as rank,
    *
FROM quarterbacks
ORDER BY yards DESC, touchdowns DESC, completion_percentage DESC;

-- RB Rankings (by yards, then TDs, then YPA)
CREATE OR REPLACE VIEW rb_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY yards DESC, touchdowns DESC, yards_per_attempt DESC) as rank,
    *
FROM running_backs
ORDER BY yards DESC, touchdowns DESC, yards_per_attempt DESC;

-- WR Rankings (by yards, then TDs, then receptions)
CREATE OR REPLACE VIEW wr_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY yards DESC, touchdowns DESC, receptions DESC) as rank,
    *
FROM wide_receivers
ORDER BY yards DESC, touchdowns DESC, receptions DESC;

-- TE Rankings (by yards, then TDs, then catches)
CREATE OR REPLACE VIEW te_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY yards DESC, touchdowns DESC, catches DESC) as rank,
    *
FROM tight_ends
ORDER BY yards DESC, touchdowns DESC, catches DESC;

-- Center Rankings (by SPS desc, then sacks allowed asc)
CREATE OR REPLACE VIEW center_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY sps DESC, sacks_allowed ASC, pressures_allowed ASC) as rank,
    *
FROM centers
ORDER BY sps DESC, sacks_allowed ASC, pressures_allowed ASC;

-- DE Rankings (by sacks, then pressures, then tackles)
CREATE OR REPLACE VIEW de_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY sacks DESC, pressures DESC, tackles DESC) as rank,
    *
FROM defensive_ends
ORDER BY sacks DESC, pressures DESC, tackles DESC;

-- LB Rankings (by interceptions, then tackles, then cmp% allowed asc)
CREATE OR REPLACE VIEW lb_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY interceptions DESC, tackles DESC, cmp_percentage ASC) as rank,
    *
FROM linebackers
ORDER BY interceptions DESC, tackles DESC, cmp_percentage ASC;

-- DB Rankings (by interceptions, then tackles, then cmp% allowed asc)
CREATE OR REPLACE VIEW db_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY interceptions DESC, tackles DESC, cmp_percentage ASC) as rank,
    *
FROM defensive_backs
ORDER BY interceptions DESC, tackles DESC, cmp_percentage ASC;

-- Kicker Rankings (by FG%, then longest, then completions)
CREATE OR REPLACE VIEW kicker_rankings AS
SELECT
    ROW_NUMBER() OVER (ORDER BY fg_percentage DESC, longest DESC, completions DESC) as rank,
    *
FROM kickers
ORDER BY fg_percentage DESC, longest DESC, completions DESC;

-- Enable Row Level Security (RLS) - Allow all operations for now
ALTER TABLE quarterbacks ENABLE ROW LEVEL SECURITY;
ALTER TABLE running_backs ENABLE ROW LEVEL SECURITY;
ALTER TABLE wide_receivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE tight_ends ENABLE ROW LEVEL SECURITY;
ALTER TABLE centers ENABLE ROW LEVEL SECURITY;
ALTER TABLE defensive_ends ENABLE ROW LEVEL SECURITY;
ALTER TABLE linebackers ENABLE ROW LEVEL SECURITY;
ALTER TABLE defensive_backs ENABLE ROW LEVEL SECURITY;
ALTER TABLE kickers ENABLE ROW LEVEL SECURITY;

-- Create policies to allow all operations (you can restrict these later)
CREATE POLICY "Allow all operations" ON quarterbacks FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON running_backs FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON wide_receivers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON tight_ends FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON centers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON defensive_ends FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON linebackers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON defensive_backs FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON kickers FOR ALL USING (true);
