Create a comprehensive NFL Fantasy League Stats Website with the following specifications:

## CORE REQUIREMENTS:

### 1. SUPABASE INTEGRATION
- Connect to Supabase database with real-time updates
- Use the provided Supabase URL and API key
- Automatically refresh data when new stats are added via Discord bot
- Display live rankings that update automatically

### 2. NFL TEAM LOGOS INTEGRATION
- When a team column displays an NFL team name, show the corresponding team logo
- Support for all 32 NFL teams plus "Free Agent" option
- Use high-quality team logos (PNG/SVG format)
- Logos should be consistent size (32x32px or 40x40px)

### 3. POSITION PAGES WITH SPECIFIC STATS

#### QUARTERBACKS PAGE
Columns: Team Logo | Team | Username | Yards | TDs | INTs | Completion% | Completions | Attempts | Sacked
- Rank by: Yards (primary), TDs (secondary), Completion% (tertiary)

#### RUNNING BACKS PAGE  
Columns: Team Logo | Team | Username | Attempts | Yards | TDs | Yards per Attempt
- Rank by: Yard<PERSON> (primary), TDs (secondary), YPA (tertiary)

#### WIDE RECEIVERS PAGE
Columns: Team Logo | Team | Username | Yards | TDs | Receptions | Targets | Reception/Targets% | INT Allowed | SNAPs
- Rank by: Yards (primary), TDs (secondary), Receptions (tertiary)

#### TIGHT ENDS PAGE
Columns: Team Logo | Team | Username | Yards | YAC | TDs | Reception/Targets% | Catches | Targets | Sack Allowed | SNAPs
- Rank by: Yards (primary), TDs (secondary), Catches (tertiary)

#### CENTERS PAGE
Columns: Team Logo | Team | Username | SNAPs | Sacks Allowed | Pressures Allowed | QB Hits Allowed | SPS (Snaps Per Sack)
- Rank by: SPS (primary, higher is better), Sacks Allowed (secondary, lower is better)

#### DEFENSIVE ENDS PAGE
Columns: Team Logo | Team | Username | Sacks | Pressures | QB Hits | Tackles | Safeties | SNAPs
- Rank by: Sacks (primary), Pressures (secondary), Tackles (tertiary)

#### LINEBACKERS PAGE
Columns: Team Logo | Team | Username | INTs | Tackles | Targets | Cmp Allowed | Cmp% | YDA | TDA | SNAPs
- Rank by: INTs (primary), Tackles (secondary), Cmp% Allowed (tertiary, lower is better)

#### DEFENSIVE BACKS PAGE
Columns: Team Logo | Team | Username | INTs | Tackles | Cmp% | Cmp Allowed | Targets | YDA | TDA | SNAPs
- Rank by: INTs (primary), Tackles (secondary), Cmp% Allowed (tertiary, lower is better)

#### KICKERS PAGE
Columns: Team Logo | Team | Username | Completions | Attempts | Longest | FG%
- Rank by: FG% (primary), Longest (secondary), Completions (tertiary)

### 4. DESIGN REQUIREMENTS

#### LAYOUT & NAVIGATION
- Modern, responsive design that works on desktop and mobile
- Top navigation bar with links to each position page
- Clean, professional NFL-themed color scheme (navy blue, silver, white)
- Sticky header with league name/logo
- Breadcrumb navigation

#### TABLE DESIGN
- Sortable columns (click to sort by any stat)
- Alternating row colors for readability
- Highlight top 3 players in each category with gold/silver/bronze indicators
- Hover effects on rows
- Search/filter functionality by team or player name
- Pagination for large datasets

#### VISUAL ELEMENTS
- NFL team logos displayed consistently
- Ranking numbers prominently displayed (#1, #2, etc.)
- Color-coded performance indicators (green for good stats, red for poor stats)
- Responsive tables that stack on mobile devices

### 5. TECHNICAL SPECIFICATIONS

#### REAL-TIME FEATURES
- Auto-refresh every 30 seconds or use Supabase real-time subscriptions
- Loading indicators when data is updating
- Toast notifications when new data is available

#### PERFORMANCE
- Lazy loading for large datasets
- Optimized queries to Supabase
- Caching for team logos
- Fast page transitions

#### ACCESSIBILITY
- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- Alt text for all images

### 6. ADDITIONAL FEATURES

#### HOMEPAGE
- League overview with top performers from each position
- Recent updates/activity feed
- Quick stats summary

#### TEAM PAGES (OPTIONAL)
- Individual team pages showing all players from that team
- Team-specific statistics and rankings

#### SEARCH & FILTERS
- Global search across all positions
- Filter by team, position, or stat ranges
- Advanced filtering options

### 7. SUPABASE CONNECTION DETAILS
- Use the ranking views (qb_rankings, rb_rankings, etc.) for automatically sorted data
- Connect to tables: quarterbacks, running_backs, wide_receivers, tight_ends, centers, defensive_ends, linebackers, defensive_backs, kickers
- Implement real-time subscriptions for live updates

### 8. MOBILE RESPONSIVENESS
- Fully responsive design
- Touch-friendly interface
- Optimized table layouts for mobile screens
- Swipe gestures for navigation

### 9. BRANDING
- Professional NFL fantasy league aesthetic
- Consistent typography and spacing
- Team colors integration where appropriate
- Modern, clean interface design

Please create a fully functional website that implements all these features with clean, maintainable code and excellent user experience. The website should feel professional and be suitable for a competitive fantasy football league.
