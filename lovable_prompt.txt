Create a comprehensive NFL Fantasy League Stats Website with the following specifications:

## CORE REQUIREMENTS:

### 1. SUPABASE INTEGRATION
- Connect to Supabase database with real-time updates
- **Supabase URL**: https://qkintlycfpxkgngykdpz.supabase.co
- **Service role  Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFraW50bHljZnB4a2duZ3lrZHB6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzMzAxNDgsImV4cCI6MjA2ODkwNjE0OH0.example_anon_key
- **Project ID**: qkintlycfpxkgngykdpz
- Automatically refresh data when new stats are added via Discord bot
- Display live rankings that update automatically
- Use Supabase client initialization with proper authentication

### 2. NFL TEAM LOGOS INTEGRATION
- When a team column displays an NFL team name, show the corresponding team logo
- Support for all 32 NFL teams plus "Free Agent" option
- Use high-quality team logos (PNG/SVG format)
- Logos should be consistent size (32x32px or 40x40px)

### 3. POSITION PAGES WITH SPECIFIC STATS

#### QUARTERBACKS PAGE
Columns: Team Logo | Team | Username | Yards | TDs | INTs | Completion% | Completions | Attempts | Sacked
- Rank by: <PERSON><PERSON> (primary), <PERSON><PERSON> (secondary), Completion% (tertiary)

#### RUNNING BACKS PAGE  
Columns: Team Logo | Team | Username | Attempts | Yards | TDs | Yards per Attempt
- Rank by: Yards (primary), TDs (secondary), YPA (tertiary)

#### WIDE RECEIVERS PAGE
Columns: Team Logo | Team | Username | Yards | TDs | Receptions | Targets | Reception/Targets% | INT Allowed | SNAPs
- Rank by: Yards (primary), TDs (secondary), Receptions (tertiary)

#### TIGHT ENDS PAGE
Columns: Team Logo | Team | Username | Yards | YAC | TDs | Reception/Targets% | Catches | Targets | Sack Allowed | SNAPs
- Rank by: Yards (primary), TDs (secondary), Catches (tertiary)

#### CENTERS PAGE
Columns: Team Logo | Team | Username | SNAPs | Sacks Allowed | Pressures Allowed | QB Hits Allowed | SPS (Snaps Per Sack)
- Rank by: SPS (primary, higher is better), Sacks Allowed (secondary, lower is better)

#### DEFENSIVE ENDS PAGE
Columns: Team Logo | Team | Username | Sacks | Pressures | QB Hits | Tackles | Safeties | SNAPs
- Rank by: Sacks (primary), Pressures (secondary), Tackles (tertiary)

#### LINEBACKERS PAGE
Columns: Team Logo | Team | Username | INTs | Tackles | Targets | Cmp Allowed | Cmp% | YDA | TDA | SNAPs
- Rank by: INTs (primary), Tackles (secondary), Cmp% Allowed (tertiary, lower is better)

#### DEFENSIVE BACKS PAGE
Columns: Team Logo | Team | Username | INTs | Tackles | Cmp% | Cmp Allowed | Targets | YDA | TDA | SNAPs
- Rank by: INTs (primary), Tackles (secondary), Cmp% Allowed (tertiary, lower is better)

#### KICKERS PAGE
Columns: Team Logo | Team | Username | Completions | Attempts | Longest | FG%
- Rank by: FG% (primary), Longest (secondary), Completions (tertiary)

### 4. DESIGN REQUIREMENTS

#### LAYOUT & NAVIGATION
- Modern, responsive design that works on desktop and mobile
- Top navigation bar with links to each position page
- Clean, professional NFL-themed color scheme (navy blue, silver, white)
- Sticky header with league name/logo
- Breadcrumb navigation

#### TABLE DESIGN
- Sortable columns (click to sort by any stat)
- Alternating row colors for readability
- Highlight top 3 players in each category with gold/silver/bronze indicators
- Hover effects on rows
- Search/filter functionality by team or player name
- Pagination for large datasets

#### VISUAL ELEMENTS
- NFL team logos displayed consistently
- Ranking numbers prominently displayed (#1, #2, etc.)
- Color-coded performance indicators (green for good stats, red for poor stats)
- Responsive tables that stack on mobile devices

### 5. TECHNICAL SPECIFICATIONS

#### REAL-TIME FEATURES
- Auto-refresh every 30 seconds or use Supabase real-time subscriptions
- Loading indicators when data is updating
- Toast notifications when new data is available

#### PERFORMANCE - OPTIMIZED FOR LARGE DATASETS
- **Pagination**: Implement robust pagination (50-100 players per page) for all position tables
- **Virtual Scrolling**: For very large datasets (1000+ players)
- **Lazy Loading**: Load data progressively as user scrolls
- **Optimized Queries**: Use Supabase range queries and proper indexing
- **Search Performance**: Fast search across large player databases
- **Caching Strategy**: Cache team logos and frequently accessed data
- **Database Optimization**: Use the ranking views for pre-sorted data
- **Loading States**: Skeleton loaders and progress indicators
- **Infinite Scroll**: Option for seamless browsing of large player lists

#### ACCESSIBILITY
- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- Alt text for all images

### 6. ADDITIONAL FEATURES

#### HOMEPAGE - REDZONE LEAGUE (RCL)
- **League Branding**: Display "REDZONE" or "RCL" prominently with league logo
- **League Description**: "Welcome to the Redzone Competitive League - The Ultimate Football Experience"
- **Discord Integration**: Prominent "Join Our Discord" button linking to discord.gg/redzones3
- **Game Information**: Feature that this is an Ultimate Football league
- **League Logo**: Display the provided league logo prominently in header and homepage
- **Hero Section**: Eye-catching banner with league branding and call-to-action to join Discord
- **League Stats Overview**:
  - Total number of players across all positions
  - Current season highlights
  - Top performers from each position (top 3 in key stats)
- **Recent Activity Feed**: Latest stat updates and player additions
- **Quick Navigation**: Easy access to all position leaderboards
- **League Information Section**:
  - About the league
  - How to join (Discord link)
  - Game rules and format
  - Season information

#### TEAM PAGES (OPTIONAL)
- Individual team pages showing all players from that team
- Team-specific statistics and rankings

#### SEARCH & FILTERS - LARGE DATASET SUPPORT
- **Global Search**: Fast search across all positions and thousands of players
- **Advanced Filters**:
  - Filter by team, position, stat ranges
  - Multi-select team filtering
  - Stat threshold filtering (e.g., show only players with >1000 yards)
  - Date range filtering for recent additions
- **Search Performance**: Debounced search with instant results
- **Filter Combinations**: Multiple filters working together
- **Saved Filters**: Allow users to save common filter combinations
- **Export Options**: Export filtered results to CSV

### 7. SUPABASE CONNECTION DETAILS
- Use the ranking views (qb_rankings, rb_rankings, etc.) for automatically sorted data
- Connect to tables: quarterbacks, running_backs, wide_receivers, tight_ends, centers, defensive_ends, linebackers, defensive_backs, kickers
- Implement real-time subscriptions for live updates

### 8. MOBILE RESPONSIVENESS
- Fully responsive design
- Touch-friendly interface
- Optimized table layouts for mobile screens
- Swipe gestures for navigation

### 9. BRANDING - REDZONE LEAGUE (RCL)
- **League Identity**: "REDZONE" or "RCL" branding throughout the site
- **Color Scheme**: Red and black primary colors (redzone theme) with NFL-inspired accents
- **League Logo**: Prominently display the provided league logo in:
  - Header/navigation bar
  - Homepage hero section
  - Footer
  - Favicon
- **Typography**: Bold, sports-inspired fonts for headers
- **Discord Integration**: "Join discord.gg/redzones3" prominently featured
- **Ultimate Football Branding**: Reference to the game "Ultimate Football"
- **Professional Aesthetic**: Clean, modern design suitable for competitive league

### 10. LEAGUE LOGO INTEGRATION
**IMPORTANT**: I will provide the Redzone League logo image. Please integrate it as follows:
- Use as the main logo in the header navigation
- Display prominently on the homepage hero section
- Include in the favicon
- Ensure it's properly sized and optimized for web use
- Maintain aspect ratio and quality across all screen sizes

### 11. LARGE DATASET CONSIDERATIONS
The website must handle potentially thousands of players across all positions:
- **Scalable Architecture**: Built to handle 10,000+ player records
- **Efficient Pagination**: Never load all records at once
- **Smart Caching**: Cache frequently accessed data
- **Progressive Loading**: Load critical data first, then enhance
- **Database Optimization**: Use Supabase's built-in ranking views
- **Search Optimization**: Fast search even with large datasets
- **Mobile Performance**: Maintain speed on mobile devices with large data

### 12. SUPABASE CONNECTION CODE EXAMPLE
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://qkintlycfpxkgngykdpz.supabase.co'
const supabaseKey = 'your_anon_key_here'
const supabase = createClient(supabaseUrl, supabaseKey)

// Example: Get QB rankings with pagination
const { data, error } = await supabase
  .from('qb_rankings')
  .select('*')
  .range(0, 49) // First 50 records
  .order('rank', { ascending: true })
```

Please create a fully functional website that implements all these features with clean, maintainable code and excellent user experience. The website should feel professional and be suitable for the competitive Redzone League (RCL) Ultimate Football community. Make sure to prominently feature the Discord link (discord.gg/redzones3) and league branding throughout the site.
