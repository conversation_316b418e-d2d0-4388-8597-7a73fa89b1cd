import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import os
from supabase import create_client, Client
from typing import Optional, Literal
import json
from dotenv import load_dotenv

# Load environment variables from .env file (optional)
load_dotenv()

# Configuration - You can either:
# 1. Replace these hardcoded values with your actual tokens/keys, OR
# 2. Create a .env file with these variables for better security
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN", "YOUR_DISCORD_BOT_TOKEN_HERE")
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://qkintlycfpxkgngykdpz.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFraW50bHljZnB4a2duZ3lrZHB6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzMzMDE0OCwiZXhwIjoyMDY4OTA2MTQ4fQ.SjBj58_6bY2qCPW62wuZW1mA3xL6oi3Nn1CqjqwI1lk")

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUP<PERSON>ASE_KEY)

# NFL Teams with their logos
NFL_TEAMS = {
    "Cardinals": "https://logos-world.net/wp-content/uploads/2020/06/Arizona-Cardinals-Logo.png",
    "Falcons": "https://logos-world.net/wp-content/uploads/2020/06/Atlanta-Falcons-Logo.png",
    "Ravens": "https://logos-world.net/wp-content/uploads/2020/06/Baltimore-Ravens-Logo.png",
    "Bills": "https://logos-world.net/wp-content/uploads/2020/06/Buffalo-Bills-Logo.png",
    "Panthers": "https://logos-world.net/wp-content/uploads/2020/06/Carolina-Panthers-Logo.png",
    "Bears": "https://logos-world.net/wp-content/uploads/2020/06/Chicago-Bears-Logo.png",
    "Bengals": "https://logos-world.net/wp-content/uploads/2020/06/Cincinnati-Bengals-Logo.png",
    "Browns": "https://logos-world.net/wp-content/uploads/2020/06/Cleveland-Browns-Logo.png",
    "Cowboys": "https://logos-world.net/wp-content/uploads/2020/06/Dallas-Cowboys-Logo.png",
    "Broncos": "https://logos-world.net/wp-content/uploads/2020/06/Denver-Broncos-Logo.png",
    "Lions": "https://logos-world.net/wp-content/uploads/2020/06/Detroit-Lions-Logo.png",
    "Packers": "https://logos-world.net/wp-content/uploads/2020/06/Green-Bay-Packers-Logo.png",
    "Texans": "https://logos-world.net/wp-content/uploads/2020/06/Houston-Texans-Logo.png",
    "Colts": "https://logos-world.net/wp-content/uploads/2020/06/Indianapolis-Colts-Logo.png",
    "Jaguars": "https://logos-world.net/wp-content/uploads/2020/06/Jacksonville-Jaguars-Logo.png",
    "Chiefs": "https://logos-world.net/wp-content/uploads/2020/06/Kansas-City-Chiefs-Logo.png",
    "Raiders": "https://logos-world.net/wp-content/uploads/2020/06/Las-Vegas-Raiders-Logo.png",
    "Chargers": "https://logos-world.net/wp-content/uploads/2020/06/Los-Angeles-Chargers-Logo.png",
    "Rams": "https://logos-world.net/wp-content/uploads/2020/06/Los-Angeles-Rams-Logo.png",
    "Dolphins": "https://logos-world.net/wp-content/uploads/2020/06/Miami-Dolphins-Logo.png",
    "Vikings": "https://logos-world.net/wp-content/uploads/2020/06/Minnesota-Vikings-Logo.png",
    "Patriots": "https://logos-world.net/wp-content/uploads/2020/06/New-England-Patriots-Logo.png",
    "Saints": "https://logos-world.net/wp-content/uploads/2020/06/New-Orleans-Saints-Logo.png",
    "Giants": "https://logos-world.net/wp-content/uploads/2020/06/New-York-Giants-Logo.png",
    "Jets": "https://logos-world.net/wp-content/uploads/2020/06/New-York-Jets-Logo.png",
    "Eagles": "https://logos-world.net/wp-content/uploads/2020/06/Philadelphia-Eagles-Logo.png",
    "Steelers": "https://logos-world.net/wp-content/uploads/2020/06/Pittsburgh-Steelers-Logo.png",
    "49ers": "https://logos-world.net/wp-content/uploads/2020/06/San-Francisco-49ers-Logo.png",
    "Seahawks": "https://logos-world.net/wp-content/uploads/2020/06/Seattle-Seahawks-Logo.png",
    "Buccaneers": "https://logos-world.net/wp-content/uploads/2020/06/Tampa-Bay-Buccaneers-Logo.png",
    "Titans": "https://logos-world.net/wp-content/uploads/2020/06/Tennessee-Titans-Logo.png",
    "Commanders": "https://logos-world.net/wp-content/uploads/2020/06/Washington-Commanders-Logo.png",
    "Free Agent": "https://cdn-icons-png.flaticon.com/512/1077/1077114.png"
}

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

# Store the stats role ID
stats_role_id = None

@bot.event
async def on_ready():
    print(f'{bot.user} has connected to Discord!')
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

def has_stats_role():
    def predicate(interaction: discord.Interaction) -> bool:
        if stats_role_id is None:
            return True  # If no role is set, allow everyone
        # Check if user is a guild member (not just a User)
        if not isinstance(interaction.user, discord.Member):
            return False
        return any(role.id == stats_role_id for role in interaction.user.roles)
    return app_commands.check(predicate)

@bot.tree.command(name="setrolestats", description="Set the required role for using stat commands")
@app_commands.describe(role="The role required to use stat commands")
async def set_role_stats(interaction: discord.Interaction, role: discord.Role):
    global stats_role_id
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
        return
    
    stats_role_id = role.id
    await interaction.response.send_message(f"Stats role set to {role.mention}. Only users with this role can use stat commands.", ephemeral=True)

async def upsert_player_stats(table_name: str, player_data: dict, username: str):
    """Insert or update player stats in the database"""
    try:
        # Check if player exists
        existing = supabase.table(table_name).select("*").eq("username", username).execute()
        
        if existing.data:
            # Update existing player
            result = supabase.table(table_name).update(player_data).eq("username", username).execute()
        else:
            # Insert new player
            result = supabase.table(table_name).insert(player_data).execute()
        
        return True, result
    except Exception as e:
        return False, str(e)

# Team choices for autocomplete
async def team_autocomplete(interaction: discord.Interaction, current: str):
    teams = list(NFL_TEAMS.keys())
    return [
        app_commands.Choice(name=team, value=team)
        for team in teams if current.lower() in team.lower()
    ][:25]

@bot.tree.command(name="enter_qb", description="Enter QB stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    yards="Passing yards",
    touchdowns="Passing touchdowns",
    interceptions="Interceptions thrown",
    completions="Completions",
    attempts="Attempts",
    sacked="Times sacked"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_qb(interaction: discord.Interaction, team: str, username: str, yards: int,
                   touchdowns: int, interceptions: int, completions: int, attempts: int, sacked: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate completion percentage automatically
    completion_percentage = round((completions / attempts * 100), 2) if attempts > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "yards": yards,
        "touchdowns": touchdowns,
        "interceptions": interceptions,
        "completion_percentage": completion_percentage,
        "completions": completions,
        "attempts": attempts,
        "sacked": sacked,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("quarterbacks", player_data, username)

    if success:
        embed = discord.Embed(title="QB Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Yards", value=yards, inline=True)
        embed.add_field(name="TDs", value=touchdowns, inline=True)
        embed.add_field(name="INTs", value=interceptions, inline=True)
        embed.add_field(name="Completion %", value=f"{completion_percentage}%", inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_rb", description="Enter RB stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    attempts="Rushing attempts",
    yards="Rushing yards",
    touchdowns="Rushing touchdowns",
    yards_per_attempt="Yards per attempt"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_rb(interaction: discord.Interaction, team: str, username: str, attempts: int,
                   yards: int, touchdowns: int, yards_per_attempt: float):
    
    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return
    
    player_data = {
        "team": team,
        "username": username,
        "attempts": attempts,
        "yards": yards,
        "touchdowns": touchdowns,
        "yards_per_attempt": yards_per_attempt,
        "team_logo": NFL_TEAMS[team]
    }
    
    success, result = await upsert_player_stats("running_backs", player_data, username)
    
    if success:
        embed = discord.Embed(title="RB Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Attempts", value=attempts, inline=True)
        embed.add_field(name="Yards", value=yards, inline=True)
        embed.add_field(name="TDs", value=touchdowns, inline=True)
        embed.add_field(name="YPA", value=yards_per_attempt, inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_wr", description="Enter WR stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    yards="Receiving yards",
    touchdowns="Receiving touchdowns",
    receptions="Receptions",
    targets="Targets",
    int_allowed="Interceptions allowed",
    snaps="Snaps played"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_wr(interaction: discord.Interaction, team: str, username: str, yards: int,
                   touchdowns: int, receptions: int, targets: int, int_allowed: int, snaps: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate reception percentage automatically
    reception_percentage = round((receptions / targets * 100), 2) if targets > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "yards": yards,
        "touchdowns": touchdowns,
        "receptions": receptions,
        "targets": targets,
        "reception_percentage": reception_percentage,
        "int_allowed": int_allowed,
        "snaps": snaps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("wide_receivers", player_data, username)

    if success:
        embed = discord.Embed(title="WR Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Yards", value=yards, inline=True)
        embed.add_field(name="TDs", value=touchdowns, inline=True)
        embed.add_field(name="Receptions", value=receptions, inline=True)
        embed.add_field(name="Reception %", value=f"{reception_percentage}%", inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_te", description="Enter TE stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    yards="Receiving yards",
    yac="Yards after catch",
    touchdowns="Receiving touchdowns",
    catches="Catches",
    targets="Targets",
    sack_allowed="Sacks allowed",
    snaps="Snaps played"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_te(interaction: discord.Interaction, team: str, username: str, yards: int,
                   yac: int, touchdowns: int, catches: int, targets: int, sack_allowed: int, snaps: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate reception percentage automatically
    reception_percentage = round((catches / targets * 100), 2) if targets > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "yards": yards,
        "yac": yac,
        "touchdowns": touchdowns,
        "reception_percentage": reception_percentage,
        "catches": catches,
        "targets": targets,
        "sack_allowed": sack_allowed,
        "snaps": snaps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("tight_ends", player_data, username)

    if success:
        embed = discord.Embed(title="TE Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Yards", value=yards, inline=True)
        embed.add_field(name="YAC", value=yac, inline=True)
        embed.add_field(name="TDs", value=touchdowns, inline=True)
        embed.add_field(name="Reception %", value=f"{reception_percentage}%", inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_c", description="Enter Center stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    snaps="Snaps played",
    sacks_allowed="Sacks allowed",
    pressures_allowed="Pressures allowed",
    qb_hits_allowed="QB hits allowed"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_c(interaction: discord.Interaction, team: str, username: str, snaps: int,
                  sacks_allowed: int, pressures_allowed: int, qb_hits_allowed: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate SPS (Snaps Per Sack) automatically
    sps = round((snaps / sacks_allowed), 2) if sacks_allowed > 0 else float(snaps)

    player_data = {
        "team": team,
        "username": username,
        "snaps": snaps,
        "sacks_allowed": sacks_allowed,
        "pressures_allowed": pressures_allowed,
        "qb_hits_allowed": qb_hits_allowed,
        "sps": sps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("centers", player_data, username)

    if success:
        embed = discord.Embed(title="Center Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Snaps", value=snaps, inline=True)
        embed.add_field(name="Sacks Allowed", value=sacks_allowed, inline=True)
        embed.add_field(name="Pressures Allowed", value=pressures_allowed, inline=True)
        embed.add_field(name="SPS", value=sps, inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_de", description="Enter DE stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    sacks="Sacks",
    pressures="Pressures",
    qb_hits="QB hits",
    tackles="Tackles",
    safeties="Safeties",
    snaps="Snaps played"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_de(interaction: discord.Interaction, team: str, username: str, sacks: int,
                   pressures: int, qb_hits: int, tackles: int, safeties: int, snaps: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    player_data = {
        "team": team,
        "username": username,
        "sacks": sacks,
        "pressures": pressures,
        "qb_hits": qb_hits,
        "tackles": tackles,
        "safeties": safeties,
        "snaps": snaps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("defensive_ends", player_data, username)

    if success:
        embed = discord.Embed(title="DE Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="Sacks", value=sacks, inline=True)
        embed.add_field(name="Pressures", value=pressures, inline=True)
        embed.add_field(name="QB Hits", value=qb_hits, inline=True)
        embed.add_field(name="Tackles", value=tackles, inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_lb", description="Enter LB stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    interceptions="Interceptions",
    tackles="Tackles",
    targets="Targets in coverage",
    cmp_allowed="Completions allowed",
    yda="Yards allowed",
    tda="TDs allowed",
    snaps="Snaps played"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_lb(interaction: discord.Interaction, team: str, username: str, interceptions: int,
                   tackles: int, targets: int, cmp_allowed: int, yda: int, tda: int, snaps: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate completion percentage allowed automatically
    cmp_percentage = round((cmp_allowed / targets * 100), 2) if targets > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "interceptions": interceptions,
        "tackles": tackles,
        "targets": targets,
        "cmp_allowed": cmp_allowed,
        "cmp_percentage": cmp_percentage,
        "yda": yda,
        "tda": tda,
        "snaps": snaps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("linebackers", player_data, username)

    if success:
        embed = discord.Embed(title="LB Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="INTs", value=interceptions, inline=True)
        embed.add_field(name="Tackles", value=tackles, inline=True)
        embed.add_field(name="Targets", value=targets, inline=True)
        embed.add_field(name="Cmp %", value=f"{cmp_percentage}%", inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_db", description="Enter DB stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    interceptions="Interceptions",
    tackles="Tackles",
    cmp_allowed="Completions allowed",
    targets="Targets in coverage",
    yda="Yards allowed",
    tda="TDs allowed",
    snaps="Snaps played"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_db(interaction: discord.Interaction, team: str, username: str, interceptions: int,
                   tackles: int, cmp_allowed: int, targets: int, yda: int, tda: int, snaps: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate completion percentage allowed automatically
    cmp_percentage = round((cmp_allowed / targets * 100), 2) if targets > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "interceptions": interceptions,
        "tackles": tackles,
        "cmp_percentage": cmp_percentage,
        "cmp_allowed": cmp_allowed,
        "targets": targets,
        "yda": yda,
        "tda": tda,
        "snaps": snaps,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("defensive_backs", player_data, username)

    if success:
        embed = discord.Embed(title="DB Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="INTs", value=interceptions, inline=True)
        embed.add_field(name="Tackles", value=tackles, inline=True)
        embed.add_field(name="Cmp %", value=f"{cmp_percentage}%", inline=True)
        embed.add_field(name="Targets", value=targets, inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="enter_k", description="Enter Kicker stats")
@app_commands.describe(
    team="NFL Team",
    username="Player username",
    completions="Field goals made",
    attempts="Field goal attempts",
    longest="Longest field goal"
)
@app_commands.autocomplete(team=team_autocomplete)
@has_stats_role()
async def enter_k(interaction: discord.Interaction, team: str, username: str, completions: int,
                  attempts: int, longest: int):

    if team not in NFL_TEAMS:
        await interaction.response.send_message("Invalid team selected.", ephemeral=True)
        return

    # Calculate field goal percentage automatically
    fg_percentage = round((completions / attempts * 100), 2) if attempts > 0 else 0.0

    player_data = {
        "team": team,
        "username": username,
        "completions": completions,
        "attempts": attempts,
        "longest": longest,
        "fg_percentage": fg_percentage,
        "team_logo": NFL_TEAMS[team]
    }

    success, result = await upsert_player_stats("kickers", player_data, username)

    if success:
        embed = discord.Embed(title="Kicker Stats Updated", color=0x00ff00)
        embed.add_field(name="Player", value=username, inline=True)
        embed.add_field(name="Team", value=team, inline=True)
        embed.add_field(name="FG Made", value=completions, inline=True)
        embed.add_field(name="FG Attempts", value=attempts, inline=True)
        embed.add_field(name="Longest", value=f"{longest} yds", inline=True)
        embed.add_field(name="FG %", value=f"{fg_percentage}%", inline=True)
        await interaction.response.send_message(embed=embed)
    else:
        await interaction.response.send_message(f"Error updating stats: {result}", ephemeral=True)

@bot.tree.command(name="help_stats", description="Show all available stat commands")
async def help_stats(interaction: discord.Interaction):
    embed = discord.Embed(title="NFL Stats Bot Commands", color=0x0099ff)
    embed.add_field(name="/enter_qb", value="Enter quarterback stats", inline=False)
    embed.add_field(name="/enter_rb", value="Enter running back stats", inline=False)
    embed.add_field(name="/enter_wr", value="Enter wide receiver stats", inline=False)
    embed.add_field(name="/enter_te", value="Enter tight end stats", inline=False)
    embed.add_field(name="/enter_c", value="Enter center stats", inline=False)
    embed.add_field(name="/enter_de", value="Enter defensive end stats", inline=False)
    embed.add_field(name="/enter_lb", value="Enter linebacker stats", inline=False)
    embed.add_field(name="/enter_db", value="Enter defensive back stats", inline=False)
    embed.add_field(name="/enter_k", value="Enter kicker stats", inline=False)
    embed.add_field(name="/setrolestats", value="Set required role for stat commands (Admin only)", inline=False)
    embed.set_footer(text="All commands support NFL teams and Free Agent option")
    await interaction.response.send_message(embed=embed)

# Run the bot
if __name__ == "__main__":
    bot.run(DISCORD_BOT_TOKEN)
