# NFL Fantasy League Discord Bot

A comprehensive Discord bot for tracking NFL fantasy league statistics with Supabase database integration and automatic website updates.

## Features

- **Discord Commands**: `/enter_qb`, `/enter_rb`, `/enter_wr`, `/enter_te`, `/enter_c`, `/enter_de`, `/enter_lb`, `/enter_db`, `/enter_k`
- **NFL Team Integration**: Support for all 32 NFL teams + Free Agent option with team logos
- **Role-Based Permissions**: `/setrolestats` command to control who can enter stats
- **Real-time Database**: Automatic Supabase integration with ranking system
- **Position-Specific Stats**: Tailored stat tracking for each NFL position

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Discord Bot Setup
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application and bot
3. Copy the bot token
4. Enable the following bot permissions:
   - Send Messages
   - Use Slash Commands
   - Read Message History
   - Embed Links

### 3. Configure Bot Token
Edit `bot.py` and replace `YOUR_DISCORD_BOT_TOKEN_HERE` with your actual Discord bot token:
```python
DISCORD_BOT_TOKEN = "your_actual_bot_token_here"
```

### 4. Supabase Setup
1. The Supabase URL and API key are already configured in the bot
2. Run the SQL schema in your Supabase SQL Editor:
   - Copy the contents of `supabase_schema.sql`
   - Paste and execute in Supabase SQL Editor
   - This creates all necessary tables, indexes, and ranking views

### 5. Invite Bot to Server
1. In Discord Developer Portal, go to OAuth2 > URL Generator
2. Select scopes: `bot` and `applications.commands`
3. Select permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`
4. Use the generated URL to invite the bot to your server

### 6. Run the Bot
```bash
python bot.py
```

## Commands

### Admin Commands
- `/setrolestats <role>` - Set the required role for using stat commands (Admin only)

### Stat Entry Commands
- `/enter_qb` - Enter quarterback stats
- `/enter_rb` - Enter running back stats  
- `/enter_wr` - Enter wide receiver stats
- `/enter_te` - Enter tight end stats
- `/enter_c` - Enter center stats
- `/enter_de` - Enter defensive end stats
- `/enter_lb` - Enter linebacker stats
- `/enter_db` - Enter defensive back stats
- `/enter_k` - Enter kicker stats

### Utility Commands
- `/help_stats` - Show all available commands

## Position Stats Tracked

### Quarterbacks
- Team, Username, Yards, TDs, INTs, Completion%, Completions, Attempts, Sacked

### Running Backs
- Team, Username, Attempts, Yards, TDs, Yards per Attempt

### Wide Receivers
- Team, Username, Yards, TDs, Receptions, Targets, Reception/Targets%, INT Allowed, SNAPs

### Tight Ends
- Team, Username, Yards, YAC, TDs, Reception/Targets%, Catches, Targets, Sack Allowed, SNAPs

### Centers
- Team, Username, SNAPs, Sacks Allowed, Pressures Allowed, QB Hits Allowed, SPS

### Defensive Ends
- Team, Username, Sacks, Pressures, QB Hits, Tackles, Safeties, SNAPs

### Linebackers
- Team, Username, INTs, Tackles, Targets, Cmp Allowed, Cmp%, YDA, TDA, SNAPs

### Defensive Backs
- Team, Username, INTs, Tackles, Cmp%, Cmp Allowed, Targets, YDA, TDA, SNAPs

### Kickers
- Team, Username, Completions, Attempts, Longest, FG%

## Website Integration

Use the `lovable_prompt.txt` file to create a website with Lovable that automatically displays and ranks all the stats from your Supabase database.

## Database Features

- **Automatic Ranking**: Views that automatically rank players by position
- **Real-time Updates**: Website updates automatically when stats are entered via Discord
- **Team Logos**: NFL team logos are automatically included
- **Stat History**: All stat updates are tracked with timestamps

## Support

If you encounter any issues:
1. Check that all dependencies are installed
2. Verify your Discord bot token is correct
3. Ensure the Supabase schema has been properly executed
4. Check bot permissions in your Discord server

## Files Included

- `bot.py` - Main Discord bot file
- `supabase_schema.sql` - Database schema for Supabase
- `lovable_prompt.txt` - Prompt for creating the website
- `requirements.txt` - Python dependencies
- `README.md` - This setup guide
